"""
测试报告功能
验证新增的轨迹图生成和阶段报告功能
"""

import numpy as np
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from staged_training_framework import LoiteringMunitionStagedTrainer
from environment_config import get_training_config

def test_trajectory_visualization():
    """测试轨迹可视化功能"""
    print("🎨 测试轨迹可视化功能")
    print("=" * 40)
    
    try:
        # 创建训练器
        trainer = LoiteringMunitionStagedTrainer(
            start_stage=1,
            end_stage=1,
            seed=42,
            visualization_interval=5  # 每5个episode生成一次图
        )
        
        # 修改配置为快速测试
        from environment_config import TRAINING_STAGES
        original_random = TRAINING_STAGES["stage1_simple"]["random_episodes"]
        original_fixed = TRAINING_STAGES["stage1_simple"]["fixed_episodes"]
        
        TRAINING_STAGES["stage1_simple"]["random_episodes"] = 8
        TRAINING_STAGES["stage1_simple"]["fixed_episodes"] = 7
        TRAINING_STAGES["stage1_simple"]["total_episodes"] = 15
        
        print(f"使用快速测试配置：8个随机 + 7个固定 = 15个episodes")
        print(f"轨迹图生成间隔：每5个episodes")
        print(f"预期生成轨迹图数量：3-4张")
        
        # 执行训练
        results, controller = trainer.run_staged_training()
        
        # 恢复原始配置
        TRAINING_STAGES["stage1_simple"]["random_episodes"] = original_random
        TRAINING_STAGES["stage1_simple"]["fixed_episodes"] = original_fixed
        TRAINING_STAGES["stage1_simple"]["total_episodes"] = original_random + original_fixed
        
        # 检查生成的文件
        output_dir = trainer.output_dir
        trajectory_files = [f for f in os.listdir(output_dir) if f.endswith('_3d_trajectory.png')]
        report_files = [f for f in os.listdir(output_dir) if 'report' in f or 'summary' in f]
        
        print(f"\n📊 生成的文件统计:")
        print(f"  轨迹图文件: {len(trajectory_files)} 张")
        for f in trajectory_files:
            print(f"    - {f}")
        
        print(f"  报告文件: {len(report_files)} 个")
        for f in report_files:
            print(f"    - {f}")
        
        print(f"\n✅ 轨迹可视化测试完成")
        print(f"📁 输出目录: {output_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 轨迹可视化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stage_reporting():
    """测试阶段报告功能"""
    print(f"\n📊 测试阶段报告功能")
    print("=" * 40)
    
    try:
        # 创建训练器
        trainer = LoiteringMunitionStagedTrainer(
            start_stage=1,
            end_stage=2,  # 测试两个阶段
            seed=42,
            visualization_interval=10
        )
        
        # 修改配置为快速测试
        from environment_config import TRAINING_STAGES
        
        # 保存原始配置
        original_configs = {}
        for stage in ["stage1_simple", "stage2_complex"]:
            original_configs[stage] = {
                "random_episodes": TRAINING_STAGES[stage]["random_episodes"],
                "fixed_episodes": TRAINING_STAGES[stage]["fixed_episodes"],
                "total_episodes": TRAINING_STAGES[stage]["total_episodes"]
            }
        
        # 设置快速测试配置
        TRAINING_STAGES["stage1_simple"]["random_episodes"] = 6
        TRAINING_STAGES["stage1_simple"]["fixed_episodes"] = 4
        TRAINING_STAGES["stage1_simple"]["total_episodes"] = 10
        
        TRAINING_STAGES["stage2_complex"]["random_episodes"] = 8
        TRAINING_STAGES["stage2_complex"]["fixed_episodes"] = 7
        TRAINING_STAGES["stage2_complex"]["total_episodes"] = 15
        
        print(f"阶段1配置：6个随机 + 4个固定 = 10个episodes")
        print(f"阶段2配置：8个随机 + 7个固定 = 15个episodes")
        
        # 执行训练
        results, controller = trainer.run_staged_training()
        
        # 恢复原始配置
        for stage, config in original_configs.items():
            TRAINING_STAGES[stage].update(config)
        
        # 检查生成的报告文件
        output_dir = trainer.output_dir
        all_files = os.listdir(output_dir)
        
        # 分类文件
        stage_reports = [f for f in all_files if f.startswith('stage_') and 'report' in f]
        stage_summaries = [f for f in all_files if f.startswith('stage_') and 'summary' in f]
        final_reports = [f for f in all_files if f.startswith('final_')]
        trajectory_files = [f for f in all_files if f.endswith('_3d_trajectory.png')]
        
        print(f"\n📊 生成的报告文件统计:")
        print(f"  阶段报告: {len(stage_reports)} 个")
        for f in sorted(stage_reports):
            print(f"    - {f}")
        
        print(f"  阶段总结图: {len(stage_summaries)} 个")
        for f in sorted(stage_summaries):
            print(f"    - {f}")
        
        print(f"  最终报告: {len(final_reports)} 个")
        for f in sorted(final_reports):
            print(f"    - {f}")
        
        print(f"  轨迹图: {len(trajectory_files)} 张")
        
        # 验证报告内容
        print(f"\n🔍 验证报告内容:")
        
        # 检查是否有阶段1和阶段2的报告
        stage1_txt = any('stage_1' in f and f.endswith('.txt') for f in stage_reports)
        stage2_txt = any('stage_2' in f and f.endswith('.txt') for f in stage_reports)
        stage1_csv = any('stage_1' in f and f.endswith('.csv') for f in stage_reports)
        stage2_csv = any('stage_2' in f and f.endswith('.csv') for f in stage_reports)
        
        print(f"  阶段1文本报告: {'✅' if stage1_txt else '❌'}")
        print(f"  阶段1CSV报告: {'✅' if stage1_csv else '❌'}")
        print(f"  阶段2文本报告: {'✅' if stage2_txt else '❌'}")
        print(f"  阶段2CSV报告: {'✅' if stage2_csv else '❌'}")
        
        # 检查最终报告
        final_txt = any(f.endswith('.txt') for f in final_reports)
        final_csv = any(f.endswith('.csv') for f in final_reports)
        final_png = any(f.endswith('.png') for f in final_reports)
        
        print(f"  最终文本报告: {'✅' if final_txt else '❌'}")
        print(f"  最终CSV报告: {'✅' if final_csv else '❌'}")
        print(f"  最终对比图: {'✅' if final_png else '❌'}")
        
        print(f"\n✅ 阶段报告测试完成")
        print(f"📁 输出目录: {output_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 阶段报告测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_report_content():
    """测试报告内容质量"""
    print(f"\n📋 测试报告内容质量")
    print("=" * 40)
    
    try:
        # 查找最近的训练输出目录
        current_dir = os.getcwd()
        output_dirs = [d for d in os.listdir(current_dir) 
                      if d.startswith('loitering_munition_staged_training_')]
        
        if not output_dirs:
            print("⚠️  没有找到训练输出目录，请先运行训练")
            return False
        
        # 使用最新的输出目录
        latest_dir = sorted(output_dirs)[-1]
        output_path = os.path.join(current_dir, latest_dir)
        
        print(f"检查目录: {latest_dir}")
        
        # 检查文本报告内容
        txt_files = [f for f in os.listdir(output_path) if f.endswith('.txt')]
        
        for txt_file in txt_files:
            file_path = os.path.join(output_path, txt_file)
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📄 {txt_file} 内容检查:")
            
            # 检查关键信息
            has_title = any(line.startswith('🎯') for line in content.split('\n'))
            has_config = '📋' in content
            has_stats = '📊' in content
            has_timestamp = '📅' in content
            
            print(f"  标题: {'✅' if has_title else '❌'}")
            print(f"  配置信息: {'✅' if has_config else '❌'}")
            print(f"  统计数据: {'✅' if has_stats else '❌'}")
            print(f"  时间戳: {'✅' if has_timestamp else '❌'}")
            
            # 显示文件大小
            file_size = len(content)
            print(f"  文件大小: {file_size} 字符")
        
        # 检查CSV文件
        csv_files = [f for f in os.listdir(output_path) if f.endswith('.csv')]
        
        for csv_file in csv_files:
            file_path = os.path.join(output_path, csv_file)
            
            print(f"\n📊 {csv_file} 内容检查:")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"  行数: {len(lines)}")
            if lines:
                print(f"  列数: {len(lines[0].split(','))}")
                print(f"  标题行: {lines[0].strip()}")
        
        print(f"\n✅ 报告内容质量检查完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 报告内容检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 报告功能测试")
    print("=" * 60)
    
    tests = [
        ("轨迹可视化", test_trajectory_visualization),
        ("阶段报告", test_stage_reporting),
        ("报告内容", test_report_content)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果摘要
    print(f"\n{'='*20} 测试结果摘要 {'='*20}")
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 测试统计: {passed}/{len(results)} 通过")
    
    if passed == len(results):
        print("🎉 所有报告功能测试通过！")
        print("📋 新增功能已成功集成:")
        print("  ✅ 定期生成3D轨迹图")
        print("  ✅ 阶段结束生成详细报告")
        print("  ✅ 最终生成对比分析")
        print("  ✅ 与简化ver1风格一致")
        return True
    else:
        print("⚠️  部分功能需要进一步调试")
        return False

if __name__ == "__main__":
    main()
