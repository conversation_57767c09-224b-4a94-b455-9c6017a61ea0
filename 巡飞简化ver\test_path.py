import os

print("Current working directory:", os.getcwd())
print()

# Test different path variations
paths_to_test = [
    "loitering_munition_staged_training_20250726_085626",
    "../loitering_munition_staged_training_20250726_085626",
    "../../loitering_munition_staged_training_20250726_085626",
    os.path.join("..", "loitering_munition_staged_training_20250726_085626"),
]

for path in paths_to_test:
    exists = os.path.exists(path)
    print(f"Path: {path}")
    print(f"Exists: {exists}")
    if exists:
        files = os.listdir(path)
        model_files = [f for f in files if f.endswith('.pth')]
        print(f"Model files found: {model_files}")
    print("-" * 50)
