# 🚀 快速入门指南

## 📋 系统要求

### 软件环境
- Python 3.7+
- NumPy
- PyTorch
- Matplotlib

### 安装依赖
```bash
pip install numpy torch matplotlib
```

## ⚡ 5分钟快速体验

### 1. 基本功能测试 (1分钟)
```bash
cd 巡飞简化ver
python simple_test.py
```
**预期输出**: 
- ✅ 环境创建成功
- ✅ DWA控制器创建成功  
- ✅ 执行控制成功
- 🎉 所有测试通过！

### 2. 系统演示 (2分钟)
```bash
python demo_fusion_system.py reward
```
**功能**: 演示不同控制输入对奖励函数的影响

### 3. 快速训练 (2分钟)
```bash
python run_staged_training.py --quick-test
```
**功能**: 运行15个episode的快速训练验证

## 🎯 主要使用场景

### 场景1: 研究人员 - 算法验证
```bash
# 1. 测试环境
python test_fusion_system.py

# 2. 查看配置
python run_staged_training.py --show-config

# 3. 单阶段训练
python run_staged_training.py --start-stage 1 --end-stage 1
```

### 场景2: 学生 - 学习理解
```bash
# 1. 系统演示
python demo_fusion_system.py

# 2. 交互式训练
python run_staged_training.py

# 选择: 3. 快速测试
```

### 场景3: 工程师 - 实际应用
```bash
# 1. 完整训练
python run_staged_training.py --start-stage 1 --end-stage 3

# 2. 自定义参数
python run_staged_training.py --start-stage 2 --end-stage 3 --seed 123
```

## 📊 输出文件说明

### 训练结果目录
```
loitering_munition_staged_training_YYYYMMDD_HHMMSS/
├── staged_training_results.json      # 训练结果摘要
├── staged_training_data.pkl          # 详细训练数据
├── stage_1_model.pth                 # 阶段1训练模型
├── stage_2_model.pth                 # 阶段2训练模型
├── stage_3_model.pth                 # 阶段3训练模型
└── Stage*_*_episode_*_trajectory.png # 轨迹可视化图片
```

### 关键文件解读

#### `staged_training_results.json`
```json
{
  "stages": {
    "stage_1": {
      "success_rate": 0.85,           // 成功率
      "final_avg_reward": 156.7,      // 平均奖励
      "training_time": 1234.5         // 训练时间(秒)
    }
  }
}
```

#### 轨迹图片
- 绿点: 起点
- 红点: 目标
- 蓝线: 飞行轨迹
- 灰色球: 静态障碍物
- 橙色球: 动态障碍物

## 🔧 常用命令

### 测试命令
```bash
# 测试特定模块
python test_fusion_system.py env      # 环境模块
python test_fusion_system.py dwa      # DWA控制器
python test_fusion_system.py td3      # TD3网络

# 演示特定功能
python demo_fusion_system.py env      # 环境可视化
python demo_fusion_system.py motion   # 运动模型
python demo_fusion_system.py dwa      # DWA控制
```

### 训练命令
```bash
# 基本训练
python run_staged_training.py

# 指定阶段
python run_staged_training.py --start-stage 2 --end-stage 3

# 自定义参数
python run_staged_training.py --seed 42 --viz-interval 5

# 显示帮助
python run_staged_training.py --help
```

## 🎛️ 参数调整

### 修改训练参数
编辑 `environment_config.py`:

```python
# 调整训练回合数
TRAINING_STAGES["stage1_simple"]["random_episodes"] = 200  # 默认150
TRAINING_STAGES["stage1_simple"]["fixed_episodes"] = 150   # 默认100

# 调整环境复杂度
ENVIRONMENT_CONFIGS["stage1_simple"]["static_obstacle_count"] = (5, 8)  # 默认(3,6)

# 调整网络参数
TD3_CONFIG["lr"] = 1e-4        # 学习率，默认3e-4
TD3_CONFIG["batch_size"] = 128  # 批次大小，默认256
```

### 修改物理参数
```python
# 巡飞弹参数
LOITERING_MUNITION_CONFIG["V_max"] = 80.0      # 最大速度，默认60.0
LOITERING_MUNITION_CONFIG["a_T_max"] = 10.0    # 最大切向加速度，默认8.0
```

## 🐛 常见问题

### Q1: 训练很慢怎么办？
**A**: 
- 减少episode数量进行快速测试
- 降低可视化频率 `--viz-interval 20`
- 使用GPU加速 `--gpu`

### Q2: 内存不足？
**A**: 
- 减少批次大小: `TD3_CONFIG["batch_size"] = 64`
- 减少缓冲区: `TD3_CONFIG["buffer_size"] = 100000`

### Q3: 训练不收敛？
**A**: 
- 增加DWA引导期: `TRAINING_CONFIG["dwa_guidance_episodes"] = 5000`
- 调整学习率: `TD3_CONFIG["lr"] = 1e-4`
- 检查奖励函数设计

### Q4: 可视化失败？
**A**: 
- 检查matplotlib安装: `pip install matplotlib`
- 确保有足够磁盘空间
- 检查文件写入权限

## 📈 性能优化建议

### 训练效率
1. **早期阶段**: 使用较少episode快速验证
2. **DWA引导**: 适当延长引导期提高安全性
3. **批次大小**: 根据内存情况调整

### 模型性能
1. **奖励函数**: 根据具体任务调整权重
2. **网络结构**: 可尝试不同隐藏层大小
3. **超参数**: 学习率、折扣因子等

### 系统稳定性
1. **随机种子**: 使用固定种子确保可重现
2. **模型保存**: 定期保存避免训练中断
3. **异常处理**: 注意观察训练日志

## 🎓 学习路径

### 初学者
1. 运行 `simple_test.py` 了解基本功能
2. 查看 `demo_fusion_system.py` 理解系统组件
3. 阅读 `README.md` 了解整体架构

### 进阶用户
1. 研究 `environment_config.py` 理解参数配置
2. 分析 `staged_training_framework.py` 学习训练流程
3. 修改配置进行自定义实验

### 专家用户
1. 扩展环境模块添加新功能
2. 改进DWA算法或替换其他算法
3. 优化TD3网络结构和训练策略

## 📞 获取帮助

### 文档资源
- `README.md`: 完整系统说明
- `FUSION_SUMMARY.md`: 融合方案总结
- 代码注释: 详细的函数和类说明

### 调试技巧
- 使用 `--quick-test` 快速验证
- 查看训练日志定位问题
- 运行单模块测试隔离问题

---

🎉 **恭喜！** 您已经掌握了巡飞弹分阶段训练系统的基本使用方法。开始您的强化学习之旅吧！
