"""
分阶段训练框架 - 融合版本
基于简化ver1的分阶段训练逻辑，适配巡飞弹的运动模型和控制
"""

import numpy as np
import torch
import time
import os
import json
import pickle
from datetime import datetime
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

from loitering_munition_environment import LoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from td3_network import StabilizedTD3Controller
from environment_config import (
    get_environment_config, get_training_stage_config, get_td3_config,
    get_loitering_munition_config, get_dwa_config, get_training_config
)

class LoiteringMunitionStagedTrainer:
    """巡飞弹分阶段训练器 - DWA+TD3职责分离版本

    设计理念：
    - DWA：负责安全约束，生成绝对安全的动作集
    - TD3：负责全局优化，从安全动作集中选择最优动作
    - 分阶段：从简单到复杂的渐进式训练
    """
    
    def __init__(self, start_stage=1, end_stage=3, seed=42, visualization_interval=10):
        self.start_stage = start_stage
        self.end_stage = end_stage
        self.seed = seed
        self.visualization_interval = visualization_interval
        
        # 设置随机种子
        np.random.seed(seed)
        torch.manual_seed(seed)
        
        # 创建输出目录
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.output_dir = f"loitering_munition_staged_training_{self.timestamp}"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 获取配置
        self.td3_config = get_td3_config()
        self.lm_config = get_loitering_munition_config()
        self.dwa_config = get_dwa_config()
        self.training_config = get_training_config()
        
        # 训练结果记录
        self.training_results = {
            "start_time": datetime.now().isoformat(),
            "config": {
                "start_stage": start_stage,
                "end_stage": end_stage,
                "seed": seed,
                "td3_config": self.td3_config,
                "lm_config": self.lm_config,
                "dwa_config": self.dwa_config
            },
            "stages": {}
        }
        
        print(f"🚁 巡飞弹分阶段训练系统 - 融合版本")
        print("=" * 60)
        print(f"📅 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 输出目录: {self.output_dir}")
        print(f"🎲 随机种子: {seed}")
        print(f"🎯 训练阶段: {start_stage} -> {end_stage}")
        print()
    
    def run_staged_training(self):
        """执行分阶段训练"""
        print(f"\n🚀 开始分阶段训练...")
        
        # 阶段名称映射
        stage_names = {
            1: "stage1_simple",
            2: "stage2_complex", 
            3: "stage3_dynamic"
        }
        
        current_controller = None
        total_start_time = time.time()
        
        # 逐阶段训练
        for stage_num in range(self.start_stage, self.end_stage + 1):
            stage_key = stage_names[stage_num]
            stage_config = get_training_stage_config(stage_key)
            env_config = get_environment_config(stage_config["environment"])
            
            print(f"\n📍 阶段 {stage_num}: {stage_config['description']}")
            print("=" * 60)
            print(f"环境: {stage_config['environment']}")
            print(f"随机场景训练: {stage_config['random_episodes']} episodes")
            print(f"固定场景训练: {stage_config['fixed_episodes']} episodes")
            print(f"总计: {stage_config['total_episodes']} episodes")
            print(f"环境描述: {env_config['description']}")
            
            try:
                # 训练当前阶段
                stage_results, trained_controller = self._train_single_stage(
                    stage_num, stage_config, env_config, current_controller
                )
                
                # 记录阶段结果
                self.training_results["stages"][f"stage_{stage_num}"] = stage_results
                current_controller = trained_controller
                
                print(f"✅ 阶段 {stage_num} 完成")
                self._print_stage_summary(stage_results)
                
            except Exception as e:
                print(f"❌ 阶段 {stage_num} 训练失败: {e}")
                self.training_results["stages"][f"stage_{stage_num}"] = {"error": str(e)}
                break
        
        # 记录总体结果
        total_time = time.time() - total_start_time
        self.training_results["end_time"] = datetime.now().isoformat()
        self.training_results["total_training_time"] = total_time
        
        # 保存训练结果
        self._save_training_results()

        # 生成最终总结报告
        self._generate_final_summary_report()

        print(f"\n🎉 分阶段训练完成!")
        print(f"⏱️  总训练时间: {total_time:.2f} 秒")
        print(f"📁 结果保存在: {self.output_dir}")

        return self.training_results, current_controller
    
    def _train_single_stage(self, stage_num, stage_config, env_config, previous_controller=None):
        """训练单个阶段"""
        stage_start_time = time.time()
        
        # 创建环境
        env = LoiteringMunitionEnvironment(
            bounds=self.lm_config['bounds'],
            environment_config=env_config,
            reward_type='simplified'
        )
        
        # 创建DWA控制器
        dwa = LoiteringMunitionDWA(dt=self.lm_config['dt'])
        
        # 创建或继承TD3控制器
        if previous_controller is not None:
            controller = previous_controller
            print(f"🔄 继承上一阶段的训练模型")
        else:
            controller = StabilizedTD3Controller(self.td3_config)
            print(f"🆕 创建新的训练模型")
        
        # 阶段训练数据
        stage_data = {
            'random_phase': {
                'episodes': [], 'rewards': [], 'success_count': 0, 'trajectories': [],
                'scenario_complexities': [], 'best_scenarios': []
            },
            'fixed_phase': {
                'episodes': [], 'rewards': [], 'success_count': 0, 'trajectories': [],
                'selected_scenario': None, 'scenario_complexity': 0
            },
            'visualization_episodes': [],
            'phase_transition_data': {}
        }
        
        global_episode_count = 0
        
        # 第一子阶段：随机场景探索
        print(f"\n🎲 随机场景探索 ({stage_config['random_episodes']} episodes)")
        print("  目标：探索不同随机场景，选择最具挑战性的场景")
        
        scenario_candidates = []
        
        for episode in range(stage_config['random_episodes']):
            # 随机生成场景
            state = env.reset()
            scenario_data = env.save_scenario()
            
            # 训练episode
            episode_reward, episode_success, trajectory, episode_stats = self._train_single_episode_detailed(
                env, controller, dwa, episode, global_episode_count, None, f"Stage{stage_num}_RandomExplore"
            )

            # 输出简洁的episode信息
            success_str = "✅" if episode_success else "❌"
            print(f"  Episode {episode+1:3d}/{stage_config['random_episodes']:3d}: {success_str} 奖励 {episode_reward:8.1f}")

            # 评估场景复杂度
            complexity_score = self._evaluate_scenario_complexity(env, episode_stats, scenario_data)
            
            # 记录场景候选
            scenario_candidates.append({
                'scenario': scenario_data,
                'complexity_score': complexity_score,
                'episode_reward': episode_reward,
                'episode_success': episode_success,
                'episode_stats': episode_stats,
                'episode_num': episode
            })
            
            # 记录数据
            stage_data['random_phase']['episodes'].append(episode)
            stage_data['random_phase']['rewards'].append(episode_reward)
            stage_data['random_phase']['scenario_complexities'].append(complexity_score)
            if episode_success:
                stage_data['random_phase']['success_count'] += 1
            
            global_episode_count += 1
            
            # 阶段性进度总结
            if (episode + 1) % 50 == 0:
                avg_reward = np.mean(stage_data['random_phase']['rewards'][-50:])
                success_rate = stage_data['random_phase']['success_count'] / (episode + 1)
                print(f"  📊 随机阶段进度 {episode + 1}/{stage_config['random_episodes']}: 平均奖励 {avg_reward:7.1f}, 成功率 {success_rate:.1%}")
        
        # 选择最具挑战性的场景
        scenario_candidates.sort(key=lambda x: x['complexity_score'], reverse=True)
        selected_scenario = scenario_candidates[0]
        stage_data['fixed_phase']['selected_scenario'] = selected_scenario['scenario']
        stage_data['fixed_phase']['scenario_complexity'] = selected_scenario['complexity_score']
        
        # 第二子阶段：固定场景强化训练
        print(f"\n📌 固定场景强化训练 ({stage_config['fixed_episodes']} episodes)")
        print(f"  目标：在最复杂场景中强化训练 (复杂度: {selected_scenario['complexity_score']:.3f})")
        
        for episode in range(stage_config['fixed_episodes']):
            # 使用固定场景
            episode_reward, episode_success, trajectory, episode_stats = self._train_single_episode_detailed(
                env, controller, dwa, episode, global_episode_count, selected_scenario['scenario'], f"Stage{stage_num}_FixedTrain"
            )

            # 输出简洁的episode信息
            success_str = "✅" if episode_success else "❌"
            print(f"  Episode {episode+1:3d}/{stage_config['fixed_episodes']:3d}: {success_str} 奖励 {episode_reward:8.1f}")

            # 记录数据
            stage_data['fixed_phase']['episodes'].append(episode)
            stage_data['fixed_phase']['rewards'].append(episode_reward)
            if episode_success:
                stage_data['fixed_phase']['success_count'] += 1
            
            global_episode_count += 1
            
            # 阶段性进度总结
            if (episode + 1) % 30 == 0:
                avg_reward = np.mean(stage_data['fixed_phase']['rewards'][-30:])
                success_rate = stage_data['fixed_phase']['success_count'] / (episode + 1)
                print(f"  📊 固定阶段进度 {episode + 1}/{stage_config['fixed_episodes']}: 平均奖励 {avg_reward:7.1f}, 成功率 {success_rate:.1%}")
        
        # 计算阶段结果
        stage_time = time.time() - stage_start_time
        
        all_rewards = stage_data['random_phase']['rewards'] + stage_data['fixed_phase']['rewards']
        total_success = stage_data['random_phase']['success_count'] + stage_data['fixed_phase']['success_count']
        total_episodes = len(all_rewards)
        
        stage_results = {
            "stage_num": stage_num,
            "config": stage_config,
            "training_time": stage_time,
            "total_episodes": total_episodes,
            "final_avg_reward": float(np.mean(all_rewards[-20:])) if len(all_rewards) >= 20 else float(np.mean(all_rewards)),
            "success_rate": total_success / total_episodes if total_episodes > 0 else 0,
            "random_phase_success_rate": stage_data['random_phase']['success_count'] / stage_config['random_episodes'] if stage_config['random_episodes'] > 0 else 0,
            "fixed_phase_success_rate": stage_data['fixed_phase']['success_count'] / stage_config['fixed_episodes'] if stage_config['fixed_episodes'] > 0 else 0,
            "selected_scenario_complexity": stage_data['fixed_phase']['scenario_complexity'],
            "all_rewards": all_rewards,
            "stage_data": stage_data
        }
        
        # 保存阶段模型
        model_path = os.path.join(self.output_dir, f"stage_{stage_num}_model.pth")
        controller.save(model_path)

        # 生成阶段报告
        self._generate_stage_report(stage_num, stage_results, stage_data)

        return stage_results, controller

    def _train_single_episode_detailed(self, env, controller, dwa, episode, global_episode, scenario_data, phase_name):
        """训练单个episode（详细版本）"""
        # 重置环境
        state = env.reset(scenario_data)

        episode_reward = 0
        episode_success = False
        trajectory = []
        episode_stats = {
            'steps': 0,
            'collisions': 0,
            'out_of_bounds': 0,
            'max_distance_to_goal': 0,
            'min_distance_to_obstacles': float('inf')
        }

        while True:
            # 记录轨迹
            trajectory.append(env.state[:3].copy())

            # 更新统计信息
            goal_dist = np.linalg.norm(env.state[:3] - env.goal)
            episode_stats['max_distance_to_goal'] = max(episode_stats['max_distance_to_goal'], goal_dist)

            # 计算到障碍物的最小距离
            for obs in env.obstacles + env.dynamic_obstacles:
                dist = np.linalg.norm(env.state[:3] - obs['center']) - obs['radius']
                episode_stats['min_distance_to_obstacles'] = min(episode_stats['min_distance_to_obstacles'], dist)

            # 选择动作 - 职责分离设计：
            # DWA：负责安全约束，生成绝对安全的动作集
            # TD3：负责全局优化，从安全动作集中选择最优动作
            safe_controls = dwa.generate_safe_control_set(
                env.state, env.obstacles + env.dynamic_obstacles, env.goal, max_actions=20
            )

            if safe_controls:
                # 将安全控制转换为归一化动作集
                safe_actions = [dwa.get_normalized_action(control) for control in safe_controls]

                # TD3直接从安全动作集中选择最优动作
                # DWA已经保证了所有动作的安全性，TD3专注于全局优化
                action = controller.select_best_action_from_safe_set(state, safe_actions)
            else:
                # 如果没有安全动作，使用紧急制动
                action = np.array([-0.5, 0.0, 0.0])

            # 将归一化动作转换为实际控制输入
            control_input = np.array([
                action[0] * env.a_T_max,
                action[1] * env.a_N_max,
                action[2] * (np.pi/2)
            ])

            # 执行动作
            next_state, reward, done, info = env.step(control_input)
            episode_reward += reward
            episode_stats['steps'] += 1

            # 记录终止原因
            if info.get('collision', False):
                episode_stats['collisions'] += 1
            if info.get('out_of_bounds', False):
                episode_stats['out_of_bounds'] += 1
            if info.get('success', False):
                episode_success = True

            # 存储经验
            controller.replay_buffer.add(
                state.copy(),
                action.copy(),
                reward,
                next_state.copy(),
                done
            )

            # 训练更新
            controller.immediate_update(batch_size=64)

            if done:
                break

            state = next_state

        # 生成可视化（每隔指定间隔）
        if (global_episode + 1) % self.visualization_interval == 0:
            self._generate_3d_trajectory_plot(env, trajectory, global_episode, episode_success, phase_name)

        return episode_reward, episode_success, trajectory, episode_stats

    def _evaluate_scenario_complexity(self, env, episode_stats, scenario_data):
        """评估场景复杂度"""
        # 基于多个因素计算复杂度分数
        complexity_score = 0.0

        # 1. 障碍物数量和密度
        static_count = len(env.obstacles)
        dynamic_count = len(env.dynamic_obstacles)
        total_obstacles = static_count + dynamic_count * 1.5  # 动态障碍物权重更高
        complexity_score += total_obstacles * 0.1

        # 2. 路径长度和曲折度
        path_length = np.linalg.norm(env.goal - env.start)
        complexity_score += path_length / 1000.0  # 归一化

        # 3. 最小障碍物距离（越小越复杂）
        if episode_stats['min_distance_to_obstacles'] < float('inf'):
            complexity_score += max(0, (50.0 - episode_stats['min_distance_to_obstacles']) / 50.0)

        # 4. 动态障碍物运动复杂度
        for obs in env.dynamic_obstacles:
            if obs['motion_type'] == 'circular':
                complexity_score += 0.3
            elif obs['motion_type'] == 'oscillating':
                complexity_score += 0.2
            else:  # linear
                complexity_score += 0.1

        # 5. 环境边界利用率
        env_volume = np.prod(env.bounds)
        obstacle_volume = sum([4/3 * np.pi * obs['radius']**3 for obs in env.obstacles + env.dynamic_obstacles])
        density = obstacle_volume / env_volume
        complexity_score += density * 2.0

        return complexity_score

    def _generate_3d_trajectory_plot(self, env, trajectory, episode, success, phase_name):
        """生成3D轨迹图 - 详细版本，清晰显示障碍物"""
        try:
            fig = plt.figure(figsize=(15, 12))
            ax = fig.add_subplot(111, projection='3d')

            # 绘制起点和终点
            start = env.start
            goal = env.goal
            ax.scatter(start[0], start[1], start[2], color='green', s=200, marker='o',
                      label='Start', edgecolors='black', linewidth=2, alpha=0.9)
            ax.scatter(goal[0], goal[1], goal[2], color='red', s=200, marker='*',
                      label='Target', edgecolors='black', linewidth=2, alpha=0.9)

            # 绘制轨迹
            trajectory = np.array(trajectory)
            if len(trajectory) > 1:
                # 根据成功状态选择轨迹颜色和样式
                if success:
                    ax.plot(trajectory[:, 0], trajectory[:, 1], trajectory[:, 2],
                           'b-', linewidth=3, alpha=0.8, label='Successful Path')
                    trajectory_color = 'blue'
                else:
                    ax.plot(trajectory[:, 0], trajectory[:, 1], trajectory[:, 2],
                           'r--', linewidth=2, alpha=0.7, label='Failed Path')
                    trajectory_color = 'red'

                # 标记最终位置
                final_pos = trajectory[-1]
                ax.scatter(final_pos[0], final_pos[1], final_pos[2],
                          color=trajectory_color, s=150, marker='X', label='End Position',
                          edgecolors='black', linewidth=2, alpha=0.9)

                # 添加轨迹点（每隔几个点显示一个）
                step_size = max(1, len(trajectory) // 15)  # 显示更多轨迹点
                for i in range(0, len(trajectory), step_size):
                    ax.scatter(trajectory[i, 0], trajectory[i, 1], trajectory[i, 2],
                              c=trajectory_color, s=15, alpha=0.5)

            # 绘制静态障碍物 - 使用详细版本的清晰渲染
            obstacle_colors = ['orange', 'yellow', 'purple', 'brown', 'pink']
            for i, obs in enumerate(env.obstacles):
                # 增加分辨率的球体参数化
                u = np.linspace(0, 2 * np.pi, 30)  # 方位角
                v = np.linspace(0, np.pi, 30)      # 极角

                # 球体坐标转换公式
                x = obs['center'][0] + obs['radius'] * np.outer(np.cos(u), np.sin(v))
                y = obs['center'][1] + obs['radius'] * np.outer(np.sin(u), np.sin(v))
                z = obs['center'][2] + obs['radius'] * np.outer(np.ones(np.size(u)), np.cos(v))

                # 使用不同颜色和更好的透明度
                color = obstacle_colors[i % len(obstacle_colors)]
                ax.plot_surface(x, y, z, alpha=0.4, color=color, edgecolor='black', linewidth=0.1,
                               label='Static Obstacles' if i == 0 else "")

                # 添加障碍物标签
                center = obs['center']
                radius = obs['radius']
                ax.text(center[0], center[1], center[2] + radius + 20,
                       f'Obs{i+1}\\nr={radius:.0f}m', fontsize=10, ha='center',
                       bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.7))

            # 绘制动态障碍物
            for i, obs in enumerate(env.dynamic_obstacles):
                # 增加分辨率的球体参数化
                u = np.linspace(0, 2 * np.pi, 30)  # 方位角
                v = np.linspace(0, np.pi, 30)      # 极角

                # 球体坐标转换公式
                x = obs['center'][0] + obs['radius'] * np.outer(np.cos(u), np.sin(v))
                y = obs['center'][1] + obs['radius'] * np.outer(np.sin(u), np.sin(v))
                z = obs['center'][2] + obs['radius'] * np.outer(np.ones(np.size(u)), np.cos(v))

                ax.plot_surface(x, y, z, alpha=0.5, color='red', edgecolor='darkred', linewidth=0.1,
                               label='Dynamic Obstacles' if i == 0 else "")

                # 添加动态障碍物标签
                center = obs['center']
                radius = obs['radius']
                ax.text(center[0], center[1], center[2] + radius + 20,
                       f'DynObs{i+1}\\nr={radius:.0f}m', fontsize=10, ha='center',
                       bbox=dict(boxstyle="round,pad=0.3", facecolor='red', alpha=0.7))

            # 设置坐标轴
            ax.set_xlim(0, env.bounds[0])
            ax.set_ylim(0, env.bounds[1])
            ax.set_zlim(0, env.bounds[2])
            ax.set_xlabel('X (m)', fontsize=12, fontweight='bold')
            ax.set_ylabel('Y (m)', fontsize=12, fontweight='bold')
            ax.set_zlabel('Z (m)', fontsize=12, fontweight='bold')
            ax.grid(True, alpha=0.3)

            # 设置等比例坐标轴，确保球体显示为圆形
            ax.set_box_aspect([env.bounds[0], env.bounds[1], env.bounds[2]])

            # 设置详细标题
            status = "SUCCESS" if success else "FAILED"
            distance_to_goal = np.linalg.norm(trajectory[-1] - goal) if len(trajectory) > 0 else float('inf')
            steps = len(trajectory)

            title = f'{phase_name} - Episode {episode+1:03d} - {status}\\n'
            title += f'Steps: {steps}, Final Distance: {distance_to_goal:.1f}m\\n'
            title += f'Obstacles: {len(env.obstacles)} Static, {len(env.dynamic_obstacles)} Dynamic'
            ax.set_title(title, fontsize=14, fontweight='bold')

            # 图例
            ax.legend(loc='upper left', bbox_to_anchor=(0, 1), fontsize=10)

            plt.tight_layout()

            # 保存图片
            filename = f"{phase_name}_episode_{episode+1:03d}_3d_trajectory.png"
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"  📊 3D轨迹图已保存: {filename}")

        except Exception as e:
            print(f"⚠️  3D轨迹图生成失败: {e}")

    def _print_stage_summary(self, stage_results):
        """打印阶段摘要"""
        print(f"\n📊 阶段 {stage_results['stage_num']} 训练摘要:")
        print(f"  ⏱️  训练时间: {stage_results['training_time']:.2f} 秒")
        print(f"  🎯 总回合数: {stage_results['total_episodes']}")
        print(f"  🏆 最终平均奖励: {stage_results['final_avg_reward']:.2f}")
        print(f"  ✅ 总体成功率: {stage_results['success_rate']:.2%}")
        print(f"  🎲 随机阶段成功率: {stage_results['random_phase_success_rate']:.2%}")
        print(f"  📌 固定阶段成功率: {stage_results['fixed_phase_success_rate']:.2%}")
        print(f"  🔥 选择场景复杂度: {stage_results['selected_scenario_complexity']:.3f}")

    def _save_training_results(self):
        """保存训练结果"""
        # 保存JSON格式的结果（处理numpy数组）
        results_file = os.path.join(self.output_dir, "staged_training_results.json")

        # 创建JSON兼容的副本
        json_results = self._convert_to_json_serializable(self.training_results)

        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(json_results, f, indent=2, ensure_ascii=False)

        # 保存pickle格式的详细数据
        data_file = os.path.join(self.output_dir, "staged_training_data.pkl")
        with open(data_file, 'wb') as f:
            pickle.dump(self.training_results, f)

        print(f"💾 训练结果已保存: {results_file}")
        print(f"💾 详细数据已保存: {data_file}")

    def _convert_to_json_serializable(self, obj):
        """将对象转换为JSON可序列化格式"""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, dict):
            return {key: self._convert_to_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_to_json_serializable(item) for item in obj]
        else:
            return obj

    def _generate_stage_report(self, stage_num, stage_results, stage_data):
        """生成阶段训练报告 - 与简化ver1一致"""
        try:
            # 生成阶段总结图表
            self._generate_stage_summary_plot(stage_num, stage_results, stage_data)

            # 生成CSV报告
            self._generate_stage_csv_report(stage_num, stage_results, stage_data)

            # 生成文本报告
            self._generate_stage_text_report(stage_num, stage_results, stage_data)

            print(f"📊 阶段 {stage_num} 报告生成完成")

        except Exception as e:
            print(f"⚠️  阶段 {stage_num} 报告生成失败: {e}")

    def _generate_stage_summary_plot(self, stage_num, stage_results, stage_data):
        """生成阶段总结图表"""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))

            # 合并两个子阶段的数据
            all_rewards = stage_data['random_phase']['rewards'] + stage_data['fixed_phase']['rewards']
            all_episodes = list(range(len(all_rewards)))
            random_episodes = len(stage_data['random_phase']['rewards'])

            # 1. 奖励曲线
            axes[0, 0].plot(all_episodes, all_rewards, 'b-', alpha=0.7, linewidth=1)
            # 添加移动平均
            if len(all_rewards) >= 10:
                window_size = min(20, len(all_rewards) // 5)
                moving_avg = np.convolve(all_rewards, np.ones(window_size)/window_size, mode='valid')
                axes[0, 0].plot(range(window_size-1, len(all_rewards)), moving_avg, 'r-', linewidth=2, label='Moving Average')

            axes[0, 0].axvline(random_episodes, color='gray', linestyle='--', alpha=0.7, label='Phase Transition')
            axes[0, 0].set_title(f'Stage {stage_num} - Episode Rewards')
            axes[0, 0].set_xlabel('Episode')
            axes[0, 0].set_ylabel('Reward')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)

            # 2. 成功率统计
            total_success = stage_data['random_phase']['success_count'] + stage_data['fixed_phase']['success_count']
            total_episodes = len(all_rewards)
            random_success_rate = stage_data['random_phase']['success_count'] / random_episodes if random_episodes > 0 else 0
            fixed_success_rate = stage_data['fixed_phase']['success_count'] / len(stage_data['fixed_phase']['rewards']) if len(stage_data['fixed_phase']['rewards']) > 0 else 0
            overall_success_rate = total_success / total_episodes if total_episodes > 0 else 0

            phases = ['Random\nPhase', 'Fixed\nPhase', 'Overall']
            success_rates = [random_success_rate, fixed_success_rate, overall_success_rate]
            colors = ['lightblue', 'lightcoral', 'lightgreen']

            bars = axes[0, 1].bar(phases, success_rates, color=colors, alpha=0.7, edgecolor='black')
            axes[0, 1].set_title(f'Stage {stage_num} - Success Rates')
            axes[0, 1].set_ylabel('Success Rate')
            axes[0, 1].set_ylim(0, 1)

            # 添加数值标签
            for bar, rate in zip(bars, success_rates):
                height = bar.get_height()
                axes[0, 1].text(bar.get_x() + bar.get_width()/2., height + 0.01,
                               f'{rate:.2%}', ha='center', va='bottom', fontweight='bold')

            # 3. 奖励分布
            axes[1, 0].hist(all_rewards, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
            axes[1, 0].axvline(np.mean(all_rewards), color='red', linestyle='--', linewidth=2, label=f'Mean: {np.mean(all_rewards):.1f}')
            axes[1, 0].set_title(f'Stage {stage_num} - Reward Distribution')
            axes[1, 0].set_xlabel('Reward')
            axes[1, 0].set_ylabel('Frequency')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

            # 4. 阶段对比
            phase_names = ['Random Phase', 'Fixed Phase']
            phase_avg_rewards = [
                np.mean(stage_data['random_phase']['rewards']) if stage_data['random_phase']['rewards'] else 0,
                np.mean(stage_data['fixed_phase']['rewards']) if stage_data['fixed_phase']['rewards'] else 0
            ]

            bars = axes[1, 1].bar(phase_names, phase_avg_rewards, color=['lightblue', 'lightcoral'], alpha=0.7, edgecolor='black')
            axes[1, 1].set_title(f'Stage {stage_num} - Phase Comparison')
            axes[1, 1].set_ylabel('Average Reward')

            # 添加数值标签
            for bar, reward in zip(bars, phase_avg_rewards):
                height = bar.get_height()
                axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                               f'{reward:.1f}', ha='center', va='bottom', fontweight='bold')

            plt.suptitle(f'Stage {stage_num} Training Summary - {stage_results["config"]["description"]}',
                        fontsize=16, fontweight='bold')
            plt.tight_layout()

            # 保存图片
            filename = f'stage_{stage_num}_training_summary.png'
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"  📊 阶段 {stage_num} 总结图已保存: {filename}")

        except Exception as e:
            print(f"⚠️  阶段 {stage_num} 总结图生成失败: {e}")

    def _generate_stage_csv_report(self, stage_num, stage_results, stage_data):
        """生成阶段CSV报告"""
        try:
            import csv

            filename = f'stage_{stage_num}_training_report.csv'
            filepath = os.path.join(self.output_dir, filename)

            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # 写入标题
                writer.writerow(['Episode', 'Phase', 'Reward', 'Success', 'Complexity'])

                # 写入随机阶段数据
                for i, (reward, complexity) in enumerate(zip(stage_data['random_phase']['rewards'],
                                                            stage_data['random_phase']['scenario_complexities'])):
                    success = i < stage_data['random_phase']['success_count']  # 简化的成功判断
                    writer.writerow([i + 1, 'Random', f"{reward:.2f}", success, f"{complexity:.3f}"])

                # 写入固定阶段数据
                random_episodes = len(stage_data['random_phase']['rewards'])
                for i, reward in enumerate(stage_data['fixed_phase']['rewards']):
                    success = i < stage_data['fixed_phase']['success_count']  # 简化的成功判断
                    complexity = stage_data['fixed_phase']['scenario_complexity']
                    writer.writerow([random_episodes + i + 1, 'Fixed', f"{reward:.2f}", success, f"{complexity:.3f}"])

            print(f"  📊 阶段 {stage_num} CSV报告已保存: {filename}")

        except Exception as e:
            print(f"⚠️  阶段 {stage_num} CSV报告生成失败: {e}")

    def _generate_stage_text_report(self, stage_num, stage_results, stage_data):
        """生成阶段文本报告"""
        try:
            filename = f'stage_{stage_num}_training_report.txt'
            filepath = os.path.join(self.output_dir, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"🎯 Stage {stage_num} Training Report\n")
                f.write("=" * 50 + "\n\n")

                # 基本信息
                f.write(f"📋 Training Configuration:\n")
                f.write(f"  • Stage: {stage_num}\n")
                f.write(f"  • Description: {stage_results['config']['description']}\n")
                f.write(f"  • Total Episodes: {stage_results['total_episodes']}\n")
                f.write(f"  • Random Phase Episodes: {len(stage_data['random_phase']['rewards'])}\n")
                f.write(f"  • Fixed Phase Episodes: {len(stage_data['fixed_phase']['rewards'])}\n")
                f.write(f"  • Training Time: {stage_results['training_time']:.2f} seconds\n\n")

                # 性能统计
                f.write(f"📊 Performance Statistics:\n")
                f.write(f"  • Overall Success Rate: {stage_results['success_rate']:.2%}\n")
                f.write(f"  • Random Phase Success Rate: {stage_results['random_phase_success_rate']:.2%}\n")
                f.write(f"  • Fixed Phase Success Rate: {stage_results['fixed_phase_success_rate']:.2%}\n")
                f.write(f"  • Final Average Reward: {stage_results['final_avg_reward']:.2f}\n")
                f.write(f"  • Selected Scenario Complexity: {stage_results['selected_scenario_complexity']:.3f}\n\n")

                # 详细分析
                all_rewards = stage_data['random_phase']['rewards'] + stage_data['fixed_phase']['rewards']
                if all_rewards:
                    f.write(f"🔍 Detailed Analysis:\n")
                    f.write(f"  • Best Reward: {max(all_rewards):.2f}\n")
                    f.write(f"  • Worst Reward: {min(all_rewards):.2f}\n")
                    f.write(f"  • Reward Standard Deviation: {np.std(all_rewards):.2f}\n")

                    if len(stage_data['random_phase']['rewards']) > 1:
                        random_trend = np.polyfit(range(len(stage_data['random_phase']['rewards'])),
                                                stage_data['random_phase']['rewards'], 1)[0]
                        f.write(f"  • Random Phase Trend: {random_trend:+.3f}/episode\n")

                    if len(stage_data['fixed_phase']['rewards']) > 1:
                        fixed_trend = np.polyfit(range(len(stage_data['fixed_phase']['rewards'])),
                                               stage_data['fixed_phase']['rewards'], 1)[0]
                        f.write(f"  • Fixed Phase Trend: {fixed_trend:+.3f}/episode\n")

                f.write(f"\n📅 Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

            print(f"  📊 阶段 {stage_num} 文本报告已保存: {filename}")

        except Exception as e:
            print(f"⚠️  阶段 {stage_num} 文本报告生成失败: {e}")

    def _generate_final_summary_report(self):
        """生成最终总结报告 - 与简化ver1一致"""
        try:
            # 生成最终对比图
            self._generate_final_comparison_plot()

            # 生成最终CSV报告
            self._generate_final_csv_report()

            # 生成最终文本报告
            self._generate_final_text_report()

            print(f"📊 最终总结报告生成完成")

        except Exception as e:
            print(f"⚠️  最终总结报告生成失败: {e}")

    def _generate_final_comparison_plot(self):
        """生成最终对比图表"""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))

            # 收集所有阶段数据
            all_stage_data = []
            stage_names = []
            stage_colors = ['lightblue', 'lightcoral', 'lightgreen']

            for stage_key, stage_result in self.training_results["stages"].items():
                if "error" not in stage_result:
                    stage_num = stage_result['stage_num']
                    all_stage_data.append(stage_result)
                    stage_names.append(f"Stage {stage_num}")

            if not all_stage_data:
                print("⚠️  没有有效的阶段数据用于生成对比图")
                return

            # 1. 成功率对比
            success_rates = [stage['success_rate'] for stage in all_stage_data]
            bars = axes[0, 0].bar(stage_names, success_rates, color=stage_colors[:len(stage_names)],
                                 alpha=0.7, edgecolor='black')
            axes[0, 0].set_title('Success Rate Comparison Across Stages', fontsize=14, fontweight='bold')
            axes[0, 0].set_ylabel('Success Rate')
            axes[0, 0].set_ylim(0, 1)

            # 添加数值标签
            for bar, rate in zip(bars, success_rates):
                height = bar.get_height()
                axes[0, 0].text(bar.get_x() + bar.get_width()/2., height + 0.01,
                               f'{rate:.2%}', ha='center', va='bottom', fontweight='bold')

            # 2. 平均奖励对比
            avg_rewards = [stage['final_avg_reward'] for stage in all_stage_data]
            bars = axes[0, 1].bar(stage_names, avg_rewards, color=stage_colors[:len(stage_names)],
                                 alpha=0.7, edgecolor='black')
            axes[0, 1].set_title('Average Reward Comparison Across Stages', fontsize=14, fontweight='bold')
            axes[0, 1].set_ylabel('Average Reward')

            # 添加数值标签
            for bar, reward in zip(bars, avg_rewards):
                height = bar.get_height()
                axes[0, 1].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                               f'{reward:.1f}', ha='center', va='bottom', fontweight='bold')

            # 3. 训练时间对比
            training_times = [stage['training_time'] for stage in all_stage_data]
            bars = axes[1, 0].bar(stage_names, training_times, color=stage_colors[:len(stage_names)],
                                 alpha=0.7, edgecolor='black')
            axes[1, 0].set_title('Training Time Comparison Across Stages', fontsize=14, fontweight='bold')
            axes[1, 0].set_ylabel('Training Time (seconds)')

            # 添加数值标签
            for bar, time_val in zip(bars, training_times):
                height = bar.get_height()
                axes[1, 0].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                               f'{time_val:.1f}s', ha='center', va='bottom', fontweight='bold')

            # 4. 场景复杂度对比
            complexities = [stage.get('selected_scenario_complexity', 0) for stage in all_stage_data]
            bars = axes[1, 1].bar(stage_names, complexities, color=stage_colors[:len(stage_names)],
                                 alpha=0.7, edgecolor='black')
            axes[1, 1].set_title('Scenario Complexity Comparison Across Stages', fontsize=14, fontweight='bold')
            axes[1, 1].set_ylabel('Scenario Complexity')

            # 添加数值标签
            for bar, complexity in zip(bars, complexities):
                height = bar.get_height()
                axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                               f'{complexity:.2f}', ha='center', va='bottom', fontweight='bold')

            plt.suptitle('Loitering Munition Staged Training - Final Comparison Report',
                        fontsize=16, fontweight='bold')
            plt.tight_layout()

            # 保存图片
            filename = 'final_training_comparison.png'
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            print(f"  📊 最终对比图已保存: {filename}")

        except Exception as e:
            print(f"⚠️  最终对比图生成失败: {e}")

    def _generate_final_csv_report(self):
        """生成最终CSV报告"""
        try:
            import csv

            filename = 'final_training_summary.csv'
            filepath = os.path.join(self.output_dir, filename)

            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # 写入标题
                writer.writerow(['Stage', 'Description', 'Total_Episodes', 'Success_Rate',
                               'Avg_Reward', 'Training_Time', 'Scenario_Complexity'])

                # 写入各阶段数据
                for stage_key, stage_result in self.training_results["stages"].items():
                    if "error" not in stage_result:
                        writer.writerow([
                            f"Stage_{stage_result['stage_num']}",
                            stage_result['config']['description'],
                            stage_result['total_episodes'],
                            f"{stage_result['success_rate']:.3f}",
                            f"{stage_result['final_avg_reward']:.2f}",
                            f"{stage_result['training_time']:.2f}",
                            f"{stage_result.get('selected_scenario_complexity', 0):.3f}"
                        ])

            print(f"  📊 最终CSV报告已保存: {filename}")

        except Exception as e:
            print(f"⚠️  最终CSV报告生成失败: {e}")

    def _generate_final_text_report(self):
        """生成最终文本报告"""
        try:
            filename = 'final_training_report.txt'
            filepath = os.path.join(self.output_dir, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write("🎯 Loitering Munition Staged Training - Final Report\n")
                f.write("=" * 60 + "\n\n")

                # 基本信息
                f.write(f"📋 Training Overview:\n")
                f.write(f"  • Start Time: {self.training_results['start_time']}\n")
                f.write(f"  • End Time: {self.training_results['end_time']}\n")
                f.write(f"  • Total Training Time: {self.training_results['total_training_time']:.2f} seconds\n")
                f.write(f"  • Stages Completed: {len([s for s in self.training_results['stages'].values() if 'error' not in s])}\n\n")

                # 各阶段总结
                f.write(f"📊 Stage-by-Stage Summary:\n")
                for stage_key, stage_result in self.training_results["stages"].items():
                    if "error" not in stage_result:
                        f.write(f"\n  Stage {stage_result['stage_num']}:\n")
                        f.write(f"    • Description: {stage_result['config']['description']}\n")
                        f.write(f"    • Episodes: {stage_result['total_episodes']}\n")
                        f.write(f"    • Success Rate: {stage_result['success_rate']:.2%}\n")
                        f.write(f"    • Average Reward: {stage_result['final_avg_reward']:.2f}\n")
                        f.write(f"    • Training Time: {stage_result['training_time']:.2f}s\n")
                        f.write(f"    • Random Phase Success: {stage_result['random_phase_success_rate']:.2%}\n")
                        f.write(f"    • Fixed Phase Success: {stage_result['fixed_phase_success_rate']:.2%}\n")
                    else:
                        f.write(f"\n  {stage_key}: FAILED - {stage_result['error']}\n")

                # 整体分析
                successful_stages = [s for s in self.training_results["stages"].values() if "error" not in s]
                if successful_stages:
                    f.write(f"\n🔍 Overall Analysis:\n")

                    success_rates = [s['success_rate'] for s in successful_stages]
                    avg_rewards = [s['final_avg_reward'] for s in successful_stages]

                    f.write(f"  • Best Success Rate: {max(success_rates):.2%}\n")
                    f.write(f"  • Average Success Rate: {np.mean(success_rates):.2%}\n")
                    f.write(f"  • Best Average Reward: {max(avg_rewards):.2f}\n")
                    f.write(f"  • Overall Average Reward: {np.mean(avg_rewards):.2f}\n")

                    # 进步分析
                    if len(successful_stages) > 1:
                        first_stage = successful_stages[0]
                        last_stage = successful_stages[-1]
                        success_improvement = last_stage['success_rate'] - first_stage['success_rate']
                        reward_improvement = last_stage['final_avg_reward'] - first_stage['final_avg_reward']

                        f.write(f"  • Success Rate Improvement: {success_improvement:+.2%}\n")
                        f.write(f"  • Reward Improvement: {reward_improvement:+.2f}\n")

                f.write(f"\n📅 Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"🎉 Training System: Loitering Munition Fusion Framework\n")

            print(f"  📊 最终文本报告已保存: {filename}")

        except Exception as e:
            print(f"⚠️  最终文本报告生成失败: {e}")
