"""
DWA分辨率实验运行器
快速启动DWA分辨率对比分析实验
"""

import sys
import os

def main():
    print("🚀 启动DWA分辨率对比分析实验")
    print("=" * 50)
    
    # 检查必要文件
    required_files = [
        "dwa_resolution_analysis.py",
        "loitering_munition_dwa.py", 
        "loitering_munition_environment.py",
        "environment_config.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("请确保所有必要文件存在后重新运行")
        return
    
    print("✅ 所有必要文件检查通过")
    print("\n🔬 开始实验...")
    
    try:
        # 导入并运行实验
        from dwa_resolution_analysis import main as run_experiment
        run_experiment()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请检查Python环境和依赖包")
    except Exception as e:
        print(f"❌ 实验运行错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
