"""
简化的GIF测试脚本 - 测试训练结果
"""

import os
import sys
import glob
import json
import numpy as np
import torch
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.animation as animation
from datetime import datetime

# 设置matplotlib使用英文字体，避免中文字体问题
plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

def find_target_training_dir():
    """查找目标训练目录"""
    print("🔍 查找训练结果目录...")
    
    # 检查上级目录中的目标训练结果
    target_dir = "../loitering_munition_staged_training_20250726_085626"
    if os.path.isdir(target_dir):
        print(f"✅ 找到目标训练目录: {target_dir}")
        return target_dir
    
    # 检查当前目录
    target_dir = "loitering_munition_staged_training_20250726_085626"
    if os.path.isdir(target_dir):
        print(f"✅ 找到目标训练目录: {target_dir}")
        return target_dir
    
    print("❌ 未找到目标训练目录")
    return None

def print_training_summary(training_dir):
    """打印训练摘要"""
    try:
        results_file = os.path.join(training_dir, 'staged_training_results.json')
        if os.path.exists(results_file):
            with open(results_file, 'r', encoding='utf-8') as f:
                results = json.load(f)

            print(f"\n📋 训练摘要 from {training_dir}:")
            print("=" * 50)

            if 'stages' in results:
                for stage_key, stage_data in results['stages'].items():
                    if 'error' not in stage_data:
                        print(f"{stage_key}:")
                        print(f"  • 总episodes: {stage_data.get('total_episodes', 'N/A')}")
                        print(f"  • 成功率: {stage_data.get('success_rate', 0):.2%}")
                        print(f"  • 平均奖励: {stage_data.get('final_avg_reward', 0):.1f}")

            print("=" * 50)

    except Exception as e:
        print(f"⚠️ 无法读取训练摘要: {e}")

def check_model_files(training_dir):
    """检查模型文件"""
    model_files = glob.glob(os.path.join(training_dir, '*_model.pth'))
    print(f"\n📦 找到模型文件:")
    for model_file in model_files:
        file_size = os.path.getsize(model_file) / (1024*1024)  # MB
        print(f"  • {os.path.basename(model_file)} ({file_size:.1f} MB)")
    
    return model_files

def create_simple_visualization(training_dir):
    """创建简单的可视化"""
    print("\n📊 创建训练结果可视化...")
    
    # 读取训练报告
    try:
        final_report = os.path.join(training_dir, 'final_training_report.txt')
        if os.path.exists(final_report):
            with open(final_report, 'r', encoding='utf-8') as f:
                content = f.read()
                print("\n📄 最终训练报告:")
                print(content[:500] + "..." if len(content) > 500 else content)
    except Exception as e:
        print(f"⚠️ 无法读取最终报告: {e}")
    
    # 读取CSV数据
    try:
        csv_file = os.path.join(training_dir, 'final_training_summary.csv')
        if os.path.exists(csv_file):
            print(f"\n📊 CSV摘要文件存在: {csv_file}")
            with open(csv_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for line in lines[:5]:  # 显示前5行
                    print(f"  {line.strip()}")
    except Exception as e:
        print(f"⚠️ 无法读取CSV文件: {e}")

def main():
    """主函数"""
    print("🎬 简化GIF测试脚本")
    print("=" * 50)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 查找训练目录
    training_dir = find_target_training_dir()
    if not training_dir:
        print("❌ 未找到训练结果目录")
        return False
    
    # 2. 打印训练摘要
    print_training_summary(training_dir)
    
    # 3. 检查模型文件
    model_files = check_model_files(training_dir)
    
    # 4. 创建简单可视化
    create_simple_visualization(training_dir)
    
    print(f"\n✅ 测试完成!")
    print(f"📁 训练目录: {training_dir}")
    print(f"📦 模型文件数量: {len(model_files)}")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print(f"\n🎉 测试成功!")
        else:
            print(f"\n❌ 测试失败")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
