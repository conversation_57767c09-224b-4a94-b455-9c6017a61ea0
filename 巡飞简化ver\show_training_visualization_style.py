"""
展示训练时生成的可视化风格
确认训练框架使用的是detailed版本的3D球体渲染
"""

import os
import matplotlib.pyplot as plt

def show_training_visualization_info():
    """展示训练可视化信息"""
    print('🎨 训练时的可视化风格说明')
    print('=' * 60)
    
    print('\n📊 训练框架现在使用的可视化特性:')
    print('✅ 高分辨率3D球体障碍物 (30x30网格)')
    print('✅ 多彩障碍物颜色区分 (橙、黄、紫、棕、粉)')
    print('✅ 清晰的障碍物标签和半径信息')
    print('✅ 黑色边框增强球体立体感')
    print('✅ 详细的轨迹点标记')
    print('✅ 起点/目标/终点的清晰标记')
    print('✅ 详细的标题信息 (步数、距离、障碍物数量)')
    print('✅ 更大的图片尺寸 (15x12)')
    
    print('\n🎯 与complete_episode_trajectory_detailed.png相同的渲染质量!')
    
    print('\n📁 生成的图片文件:')
    
    # 检查最新的训练目录
    training_dirs = [d for d in os.listdir('.') if d.startswith('loitering_munition_staged_training_')]
    if training_dirs:
        latest_dir = sorted(training_dirs)[-1]
        print(f'  最新训练目录: {latest_dir}')
        
        # 检查该目录中的图片文件
        if os.path.exists(latest_dir):
            files = os.listdir(latest_dir)
            trajectory_files = [f for f in files if f.endswith('_3d_trajectory.png')]
            
            if trajectory_files:
                print(f'  包含 {len(trajectory_files)} 个轨迹图:')
                for file in sorted(trajectory_files):
                    print(f'    - {file}')
            else:
                print('  暂无轨迹图文件')
    
    # 检查测试生成的图片
    test_files = [
        'Test_Training_episode_001_3d_trajectory.png',
        'visualization_comparison.png',
        'complete_episode_trajectory_detailed.png'
    ]
    
    print('\n🧪 测试生成的图片:')
    for file in test_files:
        if os.path.exists(file):
            print(f'  ✅ {file}')
        else:
            print(f'  ❌ {file} (未找到)')
    
    print('\n🔧 训练时的可视化设置:')
    print('  - 可视化间隔: 每10个episode生成一次图表')
    print('  - 图片格式: PNG, 300 DPI')
    print('  - 文件命名: {阶段名}_episode_{编号:03d}_3d_trajectory.png')
    print('  - 保存位置: 训练输出目录中')
    
    print('\n📋 训练时生成的图表类型:')
    print('  1. 单个episode的3D轨迹图 (每10个episode)')
    print('  2. 阶段训练总结图 (每个阶段结束时)')
    print('  3. 最终对比图 (所有阶段完成后)')
    
    print('\n🎉 总结:')
    print('训练框架已经配置为使用与complete_episode_trajectory_detailed.png')
    print('相同的高质量3D球体渲染风格，确保障碍物清晰可见！')

def demonstrate_visualization_features():
    """演示可视化特性"""
    print('\n🎨 可视化特性演示:')
    print('-' * 40)
    
    features = [
        ('3D球体障碍物', '使用30x30网格的高分辨率球面'),
        ('多彩颜色方案', '橙色、黄色、紫色、棕色、粉色循环'),
        ('黑色边框', '增强球体的立体感和边界清晰度'),
        ('障碍物标签', '显示编号和半径信息'),
        ('轨迹可视化', '成功=蓝色实线，失败=红色虚线'),
        ('关键点标记', '起点(绿圆)、目标(红星)、终点(X)'),
        ('详细标题', '包含episode、状态、步数、距离、障碍物数量'),
        ('坐标轴标签', '粗体X/Y/Z标签，网格线'),
        ('图例说明', '清晰的图例位置和字体'),
        ('高分辨率', '15x12英寸，300 DPI输出')
    ]
    
    for i, (feature, description) in enumerate(features, 1):
        print(f'  {i:2d}. {feature:<15} - {description}')
    
    print('\n✨ 这些特性确保训练过程中生成的图表具有最佳的可视化效果！')

if __name__ == "__main__":
    show_training_visualization_info()
    demonstrate_visualization_features()
