"""
完整Episode调试脚本
分析为什么巡飞弹不朝向目标移动
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import torch

from loitering_munition_environment import LoiteringMunitionEnvironment
from loitering_munition_dwa import LoiteringMunitionDWA
from td3_network import TD3Agent
from environment_config import EnvironmentConfig

def debug_complete_episode():
    """调试完整的训练episode"""
    
    # 创建环境
    config = EnvironmentConfig()
    env = LoiteringMunitionEnvironment(config)
    
    # 创建DWA算法
    dwa = LoiteringMunitionDWA()
    
    # 创建TD3网络
    state_dim = env.observation_space.shape[0]
    action_dim = env.action_space.shape[0]
    max_action = float(env.action_space.high[0])
    
    td3 = TD3Agent(state_dim, action_dim, max_action)
    
    # 重置环境
    state = env.reset()
    
    print("=== 调试完整Episode ===")
    print(f"初始状态: {state}")
    print(f"起点: {env.start}")
    print(f"目标: {env.goal}")
    print(f"初始距离: {np.linalg.norm(env.start - env.goal):.2f}m")
    
    trajectory = [env.state[:3].copy()]
    actions_taken = []
    rewards_received = []
    safe_controls_count = []
    movement_analysis = []
    
    for step in range(50):  # 限制步数以便调试
        print(f"\n--- Step {step} ---")
        print(f"当前位置: {env.state[:3]}")
        print(f"当前速度: {env.state[3]:.2f}")
        print(f"当前角度: θ={np.degrees(env.state[4]):.1f}°, φ={np.degrees(env.state[5]):.1f}°")
        
        # 计算到目标的距离
        current_dist = np.linalg.norm(env.state[:3] - env.goal)
        print(f"到目标距离: {current_dist:.2f}m")
        
        # 计算目标方向
        goal_direction = (env.goal - env.state[:3]) / (current_dist + 1e-6)
        print(f"目标方向: {goal_direction}")
        
        # DWA生成安全控制
        safe_controls = dwa.generate_safe_controls(
            env.state, env.goal, env.obstacles + env.dynamic_obstacles,
            env.bounds, env.a_T_max, env.a_N_max
        )
        
        print(f"DWA生成的安全控制数量: {len(safe_controls)}")
        safe_controls_count.append(len(safe_controls))
        
        if safe_controls:
            # 分析安全控制的方向性
            print("前3个安全控制分析:")
            for i, control in enumerate(safe_controls[:3]):
                print(f"  控制{i}: a_T={control[0]:.2f}, a_N={control[1]:.2f}, φ_dot={np.degrees(control[2]):.1f}°/s")
                
                # 预测这个控制的运动方向
                predicted_states = dwa._predict_trajectory(env.state, control, dwa.dt, 5)
                if len(predicted_states) > 1:
                    movement = predicted_states[-1][:3] - predicted_states[0][:3]
                    if np.linalg.norm(movement) > 0:
                        movement_dir = movement / np.linalg.norm(movement)
                        alignment = np.dot(goal_direction, movement_dir)
                        print(f"    预测运动方向: {movement_dir}")
                        print(f"    与目标对齐度: {alignment:.3f}")
                        print(f"    朝向目标: {'✅' if alignment > 0.5 else '❌'}")
            
            # 选择动作（模拟训练过程）
            if td3.replay_buffer.size() < 100:  # 早期阶段
                control = safe_controls[np.random.randint(min(len(safe_controls), 10))]
                action = dwa.get_normalized_action(control)
                print(f"早期阶段：随机选择安全控制")
            else:
                # 后期阶段：TD3选择
                safe_actions = [dwa.get_normalized_action(control) for control in safe_controls]
                td3_action = td3.select_action(state, noise=0.1)
                
                # 找最接近的安全动作
                best_idx = 0
                min_distance = float('inf')
                for i, safe_action in enumerate(safe_actions):
                    distance = np.linalg.norm(td3_action - safe_action)
                    if distance < min_distance:
                        min_distance = distance
                        best_idx = i
                
                action = safe_actions[best_idx]
                control = safe_controls[best_idx]
                print(f"后期阶段：TD3选择动作 {td3_action}")
                print(f"最接近的安全动作 {action}")
                print(f"对应的控制 {control}")
        else:
            # 紧急制动
            action = np.array([-0.5, 0.0, 0.0])
            control = np.array([-0.5 * env.a_T_max, 0.0, 0.0])
            print("没有安全控制，使用紧急制动")
        
        # 转换为实际控制输入
        control_input = np.array([
            action[0] * env.a_T_max,
            action[1] * env.a_N_max,
            action[2] * (np.pi/2)
        ])
        
        print(f"归一化动作: {action}")
        print(f"实际控制输入: a_T={control_input[0]:.2f}, a_N={control_input[1]:.2f}, φ_dot={np.degrees(control_input[2]):.1f}°/s")
        
        # 记录执行前的状态
        prev_pos = env.state[:3].copy()
        
        # 执行动作
        next_state, reward, done, info = env.step(control_input)
        
        # 分析实际运动
        actual_movement = next_state[:3] - prev_pos
        movement_magnitude = np.linalg.norm(actual_movement)
        
        print(f"实际位置变化: {actual_movement}")
        print(f"移动距离: {movement_magnitude:.3f}m")
        
        if movement_magnitude > 1e-6:
            actual_direction = actual_movement / movement_magnitude
            alignment = np.dot(goal_direction, actual_direction)
            print(f"实际运动方向: {actual_direction}")
            print(f"与目标对齐度: {alignment:.3f}")
            print(f"实际朝向目标: {'✅' if alignment > 0.1 else '❌'}")
        else:
            print("几乎没有移动")
            alignment = 0
        
        movement_analysis.append({
            'step': step,
            'movement': actual_movement,
            'magnitude': movement_magnitude,
            'alignment': alignment,
            'goal_distance': current_dist
        })
        
        print(f"奖励: {reward:.2f}")
        print(f"新位置: {next_state[:3]}")
        
        # 记录数据
        trajectory.append(next_state[:3].copy())
        actions_taken.append(action.copy())
        rewards_received.append(reward)
        
        # 更新状态
        state = next_state
        
        if done:
            print(f"Episode结束: {info}")
            break
    
    # 详细分析结果
    print(f"\n=== 详细Episode分析 ===")
    print(f"总步数: {len(trajectory)-1}")
    print(f"总奖励: {sum(rewards_received):.2f}")
    print(f"最终距离: {np.linalg.norm(state[:3] - env.goal):.2f}m")
    print(f"平均安全控制数量: {np.mean(safe_controls_count):.1f}")
    
    # 分析轨迹移动
    trajectory = np.array(trajectory)
    if len(trajectory) > 1:
        total_distance = 0
        for i in range(1, len(trajectory)):
            total_distance += np.linalg.norm(trajectory[i] - trajectory[i-1])
        print(f"总移动距离: {total_distance:.2f}m")
        print(f"平均每步移动: {total_distance/(len(trajectory)-1):.3f}m")
        
        # 分析整体方向
        overall_movement = trajectory[-1] - trajectory[0]
        overall_distance = np.linalg.norm(overall_movement)
        if overall_distance > 0:
            overall_direction = overall_movement / overall_distance
            goal_direction_overall = (env.goal - trajectory[0]) / np.linalg.norm(env.goal - trajectory[0])
            overall_alignment = np.dot(goal_direction_overall, overall_direction)
            print(f"整体移动方向: {overall_direction}")
            print(f"整体与目标对齐度: {overall_alignment:.3f}")
            print(f"整体朝向目标: {'✅' if overall_alignment > 0.1 else '❌'}")
    
    # 分析运动模式
    if movement_analysis:
        alignments = [m['alignment'] for m in movement_analysis]
        magnitudes = [m['magnitude'] for m in movement_analysis]
        
        print(f"平均对齐度: {np.mean(alignments):.3f}")
        print(f"正向移动步数: {sum(1 for a in alignments if a > 0.1)}/{len(alignments)}")
        print(f"平均移动距离: {np.mean(magnitudes):.3f}m")
        print(f"最大移动距离: {np.max(magnitudes):.3f}m")
        print(f"最小移动距离: {np.min(magnitudes):.3f}m")
    
    return trajectory, actions_taken, rewards_received, movement_analysis

def analyze_dwa_controls():
    """专门分析DWA生成的控制"""
    print("\n=== DWA控制分析 ===")
    
    # 创建环境和DWA
    config = EnvironmentConfig()
    env = LoiteringMunitionEnvironment(config)
    dwa = LoiteringMunitionDWA()
    
    # 重置环境
    state = env.reset()
    
    print(f"当前状态: {env.state}")
    print(f"目标位置: {env.goal}")
    
    # 生成安全控制
    safe_controls = dwa.generate_safe_controls(
        env.state, env.goal, env.obstacles + env.dynamic_obstacles,
        env.bounds, env.a_T_max, env.a_N_max
    )
    
    print(f"生成的安全控制数量: {len(safe_controls)}")
    
    if safe_controls:
        goal_direction = (env.goal - env.state[:3]) / np.linalg.norm(env.goal - env.state[:3])
        print(f"目标方向: {goal_direction}")
        
        # 分析所有安全控制
        positive_controls = 0
        for i, control in enumerate(safe_controls):
            # 预测轨迹
            predicted_states = dwa._predict_trajectory(env.state, control, dwa.dt, 10)
            if len(predicted_states) > 1:
                movement = predicted_states[-1][:3] - predicted_states[0][:3]
                if np.linalg.norm(movement) > 0:
                    movement_dir = movement / np.linalg.norm(movement)
                    alignment = np.dot(goal_direction, movement_dir)
                    
                    if alignment > 0.1:
                        positive_controls += 1
                    
                    if i < 10:  # 只显示前10个
                        print(f"控制{i}: {control} -> 对齐度: {alignment:.3f}")
        
        print(f"朝向目标的控制数量: {positive_controls}/{len(safe_controls)}")
        print(f"朝向目标的比例: {positive_controls/len(safe_controls)*100:.1f}%")
    
    return safe_controls

if __name__ == "__main__":
    # 先分析DWA控制
    safe_controls = analyze_dwa_controls()
    
    # 然后调试完整episode
    trajectory, actions, rewards, movement_analysis = debug_complete_episode()
