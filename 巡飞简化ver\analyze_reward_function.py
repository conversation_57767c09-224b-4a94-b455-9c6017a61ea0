"""
奖励函数分析脚本
详细分析奖励函数各组件的贡献和特性
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from loitering_munition_environment import LoiteringMunitionEnvironment
from environment_config import get_environment_config, get_loitering_munition_config

def analyze_reward_components():
    """分析奖励函数各组件"""
    print("🎯 奖励函数组件详细分析")
    print("=" * 50)
    
    # 创建环境
    env_config = get_environment_config('test_simple')
    lm_config = get_loitering_munition_config()
    
    env = LoiteringMunitionEnvironment(
        bounds=[1000, 1000, 100],
        environment_config=env_config,
        reward_type='simplified'
    )
    
    # 设置简单场景便于分析
    env.start = np.array([100, 100, 50])
    env.goal = np.array([800, 800, 50])
    env.obstacles = []
    env.dynamic_obstacles = []
    
    state = env.reset()
    
    print(f"场景设置:")
    print(f"  起点: {env.start}")
    print(f"  目标: {env.goal}")
    print(f"  初始距离: {np.linalg.norm(env.start - env.goal):.2f}m")
    print(f"  初始状态: {env.state}")
    print()
    
    # 测试不同控制输入
    test_controls = [
        ([0, 0, 0], "无控制"),
        ([4, 0, 0], "纯加速"),
        ([-4, 0, 0], "纯减速"),
        ([0, 20, 0], "纯转向"),
        ([0, 0, 0.5], "纯倾斜"),
        ([2, 10, 0], "加速+转向"),
        ([2, 10, 0.2], "组合控制"),
        ([-2, -10, 0], "减速+反向"),
    ]
    
    results = []
    
    for control, description in test_controls:
        # 重置环境到相同状态
        env.reset()
        
        # 执行控制
        next_state, reward, done, info = env.step(np.array(control))
        
        # 计算各种距离和状态
        new_pos = env.state[:3]
        new_dist = np.linalg.norm(new_pos - env.goal)
        distance_change = np.linalg.norm(env.start - env.goal) - new_dist
        new_speed = env.state[3]
        
        results.append({
            'description': description,
            'control': control,
            'reward': reward,
            'new_distance': new_dist,
            'distance_change': distance_change,
            'new_speed': new_speed,
            'new_position': new_pos,
            'done': done,
            'info': info
        })
        
        print(f"{description:12s}: 奖励 {reward:7.2f}, 距离 {new_dist:6.2f}m, "
              f"变化 {distance_change:6.2f}m, 速度 {new_speed:5.2f}m/s")
    
    return results

def analyze_reward_sensitivity():
    """分析奖励函数对参数的敏感性"""
    print(f"\n🔍 奖励函数敏感性分析")
    print("=" * 50)
    
    # 创建环境
    env_config = get_environment_config('test_simple')
    lm_config = get_loitering_munition_config()
    
    env = LoiteringMunitionEnvironment(
        bounds=[1000, 1000, 100],
        environment_config=env_config,
        reward_type='simplified'
    )
    
    # 设置场景
    env.start = np.array([100, 100, 50])
    env.goal = np.array([800, 800, 50])
    env.obstacles = []
    env.dynamic_obstacles = []
    
    # 测试不同距离下的奖励
    distances = np.linspace(50, 1000, 20)
    rewards = []
    
    for dist in distances:
        # 设置不同的位置
        angle = np.pi / 4  # 45度角
        pos = env.goal - np.array([dist * np.cos(angle), dist * np.sin(angle), 0])
        
        env.reset()
        env.state[:3] = pos
        env._prev_goal_dist = np.linalg.norm(pos - env.goal)
        
        # 执行小幅前进
        next_state, reward, done, info = env.step(np.array([1, 5, 0]))
        rewards.append(reward)
    
    # 绘制距离-奖励关系
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.plot(distances, rewards, 'b-', linewidth=2)
    plt.xlabel('距离目标距离 (m)')
    plt.ylabel('奖励值')
    plt.title('距离 vs 奖励关系')
    plt.grid(True)
    
    # 测试不同速度下的奖励
    speeds = np.linspace(10, 50, 20)
    speed_rewards = []
    
    for speed in speeds:
        env.reset()
        env.state[3] = speed  # 设置速度
        
        next_state, reward, done, info = env.step(np.array([0, 0, 0]))
        speed_rewards.append(reward)
    
    plt.subplot(2, 2, 2)
    plt.plot(speeds, speed_rewards, 'r-', linewidth=2)
    plt.axvline(x=25, color='g', linestyle='--', label='目标速度')
    plt.xlabel('飞行速度 (m/s)')
    plt.ylabel('奖励值')
    plt.title('速度 vs 奖励关系')
    plt.legend()
    plt.grid(True)
    
    # 测试不同方向角度的奖励
    angles = np.linspace(0, 2*np.pi, 36)
    angle_rewards = []
    
    for angle in angles:
        env.reset()
        # 设置朝向不同方向的速度
        env.state[5] = angle  # 偏航角
        
        next_state, reward, done, info = env.step(np.array([0, 0, 0]))
        angle_rewards.append(reward)
    
    plt.subplot(2, 2, 3)
    plt.polar(angles, angle_rewards)
    plt.title('飞行方向 vs 奖励关系')
    
    # 奖励组件贡献分析
    components = ['距离改善', '进度奖励', '前进奖励', '安全奖励', '速度奖励', '时间惩罚']
    typical_values = [5.0, 15.0, 8.0, 3.0, 4.0, -0.5]
    
    plt.subplot(2, 2, 4)
    colors = ['blue', 'green', 'red', 'orange', 'purple', 'brown']
    plt.bar(components, typical_values, color=colors, alpha=0.7)
    plt.xlabel('奖励组件')
    plt.ylabel('典型值')
    plt.title('各组件典型贡献')
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    plt.savefig('reward_function_analysis.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("📊 敏感性分析完成，图表已保存为 reward_function_analysis.png")

def compare_reward_strategies():
    """比较不同奖励策略"""
    print(f"\n⚖️  奖励策略对比")
    print("=" * 50)
    
    # 创建两个环境：简化奖励 vs 基础奖励
    env_config = get_environment_config('test_simple')
    lm_config = get_loitering_munition_config()
    
    env_simplified = LoiteringMunitionEnvironment(
        bounds=[1000, 1000, 100],
        environment_config=env_config,
        reward_type='simplified'
    )
    
    env_basic = LoiteringMunitionEnvironment(
        bounds=[1000, 1000, 100],
        environment_config=env_config,
        reward_type='basic'
    )
    
    # 设置相同场景
    for env in [env_simplified, env_basic]:
        env.start = np.array([100, 100, 50])
        env.goal = np.array([800, 800, 50])
        env.obstacles = []
        env.dynamic_obstacles = []
    
    # 测试相同的控制序列
    test_controls = [
        [2, 10, 0.1],
        [1, 5, 0],
        [0, 15, 0],
        [3, 0, 0],
        [-1, 8, 0.2]
    ]
    
    print("控制输入        简化奖励    基础奖励    差异")
    print("-" * 50)
    
    for i, control in enumerate(test_controls):
        # 重置两个环境
        env_simplified.reset()
        env_basic.reset()
        
        # 执行相同控制
        _, reward_simplified, _, _ = env_simplified.step(np.array(control))
        _, reward_basic, _, _ = env_basic.step(np.array(control))
        
        difference = reward_simplified - reward_basic
        
        print(f"控制 {i+1:2d}:        {reward_simplified:8.2f}   {reward_basic:8.2f}   {difference:8.2f}")
    
    print(f"\n💡 分析结果:")
    print(f"  - 简化奖励函数提供更丰富的反馈信息")
    print(f"  - 基础奖励函数相对简单，可能导致学习困难")
    print(f"  - 简化版本能更好地引导智能体学习")

def main():
    """主函数"""
    print("🚀 奖励函数全面分析")
    print("=" * 60)
    
    try:
        # 1. 组件分析
        results = analyze_reward_components()
        
        # 2. 敏感性分析
        analyze_reward_sensitivity()
        
        # 3. 策略对比
        compare_reward_strategies()
        
        print(f"\n🎉 分析完成!")
        print(f"📁 生成文件:")
        print(f"  - reward_function_analysis.png: 奖励函数可视化分析")
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
